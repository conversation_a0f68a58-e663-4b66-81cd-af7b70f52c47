# Derental Deployment Workflow Documentation

This document outlines the complete process of how backend and Dev branch code changes make their way to the master branch and eventually to production environments.

## Overview

The Derental project follows a multi-environment deployment strategy with separate workflows for frontend (web) and backend components. The deployment process involves multiple environments: Dev, Staging (Pre), Demo, and Production.

## Branch Structure

- **`dev`** - Development branch for frontend changes
- **`backend`** - Development branch for backend changes
- **`master`** - Main branch for staging deployments
- **Production tags** - Tagged releases for production deployments

## Deployment Environments

| Environment       | Purpose                | Trigger                             | GCP Project     |
| ----------------- | ---------------------- | ----------------------------------- | --------------- |
| **Dev**           | Development testing    | PR merge to `backend`/`dev`         | `dev-derental`  |
| **Staging (Pre)** | Pre-production testing | PR merge to `master`                | `pre-derental`  |
| **Demo**          | Client demonstrations  | Tagged release `v*.*.*-demo*`       | `demo-derental` |
| **Production**    | Live environment       | Tagged release `v*.*.*-backend/web` | `prod-derental` |

## Frontend (Web) Deployment Flow

### 1. Development Phase

```
Feature Branch → dev branch → Pull Request
Feature Branch → backend branch → Pull Request
```

**Process for a frontend deploy to Dev:**

1. Developer creates feature branch from `dev`
2. Implements changes and creates PR to `dev` branch
3. **Automated CI/CD triggers:**

   - Build and test workflow (`.github/workflows/build_web.yml`)
   - Runs: `yarn build`, `yarn test`, `yarn lint`
   - Uses Node.js 18.x with Yarn caching

**Process for a backend deploy to Dev:**

4. Developer creates feature branch from `backend`
5. Implements changes and creates PR to `backend` branch
6. **Automated CI/CD triggers:**
   - Build and test workflow (`.github/workflows/build_backend.yml`)
   - Runs: `go build`, `go test`, `golangci-lint`
   - Uses Go 1.23

### 2. Dev Environment Deployment

```
PR merged to dev → Automatic deployment to Dev environment for fronted work
PR merged to backend → Automatic deployment to Dev environment for backend work
```

**Trigger:** PR closed/merged to `dev` branch  
**Workflow:** `.github/workflows/dev_web.yml`  
**Deployment:** Firebase Hosting (Dev project)

### 3. Staging Deployment

```
dev branch → master branch → Automatic deployment to Staging
backend branch → master branch → Automatic deployment to Staging
```

**Process:**

1. Create PR from `dev` to `master`
2. **Automated CI/CD triggers:**

   - Workflow: `.github/workflows/pre_web.yml`
   - Deploys to Firebase Hosting (Staging project)

   **Process:**

3. Create PR from `dev` to `master`
4. **Automated CI/CD triggers:**
   - Workflow: `.github/workflows/pre_web.yml`
   - Deploys to Firebase Hosting (Staging project)

### 4. Production Deployment

```
master branch → Create production tag → Automatic deployment to Production
```

**Process:**

1. Create production tag: `v[0-9].[0-9]+.[0-9]+-web` (e.g., `v1.2.3-web`)
2. **Automated CI/CD triggers:**
   - Workflow: `.github/workflows/prod_web.yml`
   - Deploys to Firebase Hosting (Production project)

## Backend Deployment Flow

### 1. Development Phase

```
Feature Branch → backend branch → Pull Request
```

**Process:**

1. Developer creates feature branch from `backend`
2. Implements changes and creates PR to `backend` branch
3. **Automated CI/CD triggers:**
   - Build and test workflow (`.github/workflows/build_backend.yml`)
   - Runs: `go build`, `go test`, `golangci-lint`
   - Uses Go 1.23

### 2. Dev Environment Deployment

```
PR merged to backend → Automatic deployment to Dev environment
```

**Trigger:** PR closed/merged to `backend` branch  
**Workflow:** `.github/workflows/dev_backend.yml`  
**Deployment:** Google Cloud Run (Dev project)

**Services deployed:**

- `tooler` (main API)
- `tooler-equipment-uploader`
- `derental-crone-job`

### 3. Staging Deployment

```
backend branch → master branch → Automatic deployment to Staging
```

**Process:**

1. Create PR from `backend` to `master`
2. **Automated CI/CD triggers:**
   - Workflow: `.github/workflows/pre_backend.yml`
   - Deploys to Google Cloud Run (Staging project)

### 4. Production Deployment

```
master branch → Create production tag → Automatic deployment to Production
```

**Process:**

1. Create production tag: `v[0-9].[0-9]+.[0-9]+-backend` (e.g., `v1.2.3-backend`)
2. **Automated CI/CD triggers:**
   - Workflow: `.github/workflows/prod_backend.yml`
   - Deploys to Google Cloud Run (Production project)

## Demo Environment

### Frontend Demo

**Trigger:** Tag `v[0-9].[0-9]+.[0-9]+-demoWeb`  
**Workflow:** `.github/workflows/demo_web.yml`

### Backend Demo

**Trigger:** Tag `v[0-9].[0-9]+.[0-9]+-demoBackend`  
**Workflow:** `.github/workflows/demo_backend.yml`

## Manual Deployment Commands

### Backend Manual Deployment

Located in `backend/makefile`:

```bash
# Build Docker image
make build

# Push to registry
make push

# Deploy to Cloud Run
make deploy

# Deploy equipment uploader service
make deploy-equipment-uploader
```

### Frontend Manual Deployment

Located in `package.json`:

```bash
# Deploy to Firebase Hosting
yarn deploy
# or
npm run deploy
```

## Environment Configuration

### Backend Environment Variables

Each environment uses different configurations:

- `MODE=production`
- `GOOGLE_CLOUD_PROJECT`
- `FIREBASE_API_KEY`
- `FIREBASE_STORAGE_BUCKET`
- `EQUIPMENT_LIBRARY_URL`
- `SENDGRID_*` variables
- `SLACK_WEBHOOK_URL`

### Frontend Environment Variables

Environment-specific variables in `.env`:

- `VITE_REACT_APP_BASE_URL`
- `VITE_ALGOLIA_*` variables
- `VITE_DERENTAL_*` variables

## Quality Gates

### Pre-commit Hooks

- **Husky** pre-commit hook runs `npm run lint`
- Located in `.husky/pre-commit`

### Automated Testing

- **Frontend:** Jest tests, ESLint, build verification
- **Backend:** Go tests with race detection, golangci-lint

## Infrastructure

### Google Cloud Platform

- **Container Registry:** `us-central1-docker.pkg.dev`
- **Cloud Run:** Serverless container deployment
- **Region:** `us-central1`
- **Scaling:** 0-30 instances, 500 concurrency

### Firebase

- **Hosting:** Frontend deployment
- **Storage:** File storage
- **Authentication:** User management

## Rollback Strategy

### Production Rollback

1. **Tag-based rollback:** Deploy previous working tag
2. **Manual rollback:** Use GCP Console or CLI commands
3. **Database rollback:** Manual intervention required

### Emergency Procedures

1. Identify the issue
2. Revert to last known good tag
3. Deploy emergency fix if needed
4. Post-incident review and documentation

## Monitoring and Alerts

- **GCP Monitoring:** Cloud Run metrics
- **Firebase Analytics:** Frontend performance monitoring

## Best Practices

1. **Always test in Dev environment first**
2. **Use staging for final validation**
3. **Create descriptive commit messages**
4. **Tag releases with semantic versioning**
5. **Monitor deployments for issues**
6. **Keep environment configurations in sync**

## Troubleshooting

### Common Issues

1. **Build failures:** Check dependencies and environment variables
2. **Deployment timeouts:** Verify GCP quotas and permissions
3. **Environment mismatches:** Validate configuration variables
4. **Database connection issues:** Check firewall rules and credentials

### Support Contacts

- **DevOps:** Check GCP Console and logs
- **Frontend Issues:** Check Firebase Console
- **Backend Issues:** Check Cloud Run logs
