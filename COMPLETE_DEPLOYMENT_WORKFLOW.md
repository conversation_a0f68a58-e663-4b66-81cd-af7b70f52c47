# 🚀 Derental Complete Deployment Workflow Documentation

This document outlines the complete deployment process for the Derental project, ensuring that only high-quality code reaches production. We operate with four environments: `dev`, `preprod`, `demo`, and `prod`.

## 🏗️ Overview

The Derental project follows a multi-environment deployment strategy with separate workflows for frontend (web) and backend components. The deployment process involves rigorous quality gates, peer reviews, and manual QA validation at each stage.

## 🌿 Branch Structure

- **`dev`** - Development branch for frontend changes
- **`backend`** - Development branch for backend changes
- **`master`** - Main branch for staging/preprod deployments
- **Feature branches** - Individual feature development branches
- **Demo tags** - Tagged releases for demo deployments
- **Production tags** - Tagged releases for production deployments

## 🌍 Deployment Environments

| Environment           | Purpose                                  | Trigger                     | GCP Project     | Frontend Tag     | Backend Tag          |
| --------------------- | ---------------------------------------- | --------------------------- | --------------- | ---------------- | -------------------- |
| **Dev**               | Development testing & Product validation | PR merge to `backend`/`dev` | `dev-derental`  | Auto-deploy      | Auto-deploy          |
| **Preprod (Staging)** | Pre-production testing & QA              | PR merge to `master`        | `pre-derental`  | Auto-deploy      | Auto-deploy          |
| **Demo**              | Client demonstrations                    | Tagged release              | `demo-derental` | `v*.*.*-demoWeb` | `v*.*.*-demoBackend` |
| **Production**        | Live environment                         | Tagged release              | `prod-derental` | `v*.*.*-web`     | `v*.*.*-backend`     |

## 🧩 1. Development Phase

### Frontend Development

- Developers work on **feature branches** from `dev` branch
- Each commit should:
  - Be atomic and clearly scoped
  - Pass local linters and unit tests
  - Follow naming conventions and coding standards

### Backend Development

- Developers work on **feature branches** from `backend` branch
- Each commit should:
  - Be atomic and clearly scoped
  - Pass local Go tests and linting
  - Follow Go best practices and project conventions

## 🔁 2. Pull Request (PR) Phase

### Frontend PR Process

**Target:** `dev` branch  
**Automated CI/CD triggers:**

- Build and test workflow (`.github/workflows/build_web.yml`)
- Runs: `yarn build`, `yarn test`, `yarn lint`
- Uses Node.js 18.x with Yarn caching

### Backend PR Process

**Target:** `backend` branch  
**Automated CI/CD triggers:**

- Build and test workflow (`.github/workflows/build_backend.yml`)
- Runs: `go build`, `go test`, `golangci-lint`
- Uses Go 1.23

### **Code Review Requirements (Both FE & BE)**

- **Mandatory review** by at least one other developer
- Ensure code quality, readability, performance, and security
- Validate test coverage and feature scope

### **Checklist Before Merge**

- ✅ Code reviewed and approved
- ✅ Unit tests updated or added
- ✅ All CI checks passed (lint, test, build)
- ✅ No merge conflicts

## 🚀 3. Dev Environment Deployment

### Frontend Dev Deployment

```
PR merged to dev → Automatic deployment to Dev environment
```

**Trigger:** PR closed/merged to `dev` branch  
**Workflow:** `.github/workflows/dev_web.yml`  
**Deployment:** Firebase Hosting (Dev project)

### Backend Dev Deployment

```
PR merged to backend → Automatic deployment to Dev environment
```

**Trigger:** PR closed/merged to `backend` branch  
**Workflow:** `.github/workflows/dev_backend.yml`  
**Deployment:** Google Cloud Run (Dev project)

**Services deployed:**

- `tooler` (main API)
- `tooler-equipment-uploader`
- `derental-crone-job`

### **Dev Environment Purpose**

- **Product validation:** Deploy on Dev env → Product team visualizes work → Approved or needs reshaping
- **Initial testing:** Basic functionality verification
- **Requirements alignment:** Ensure 100% alignment between dev and product teams
- **911 fixes:** Quick deployment for urgent fixes requiring product validation

## 🧪 4. Manual QA & Preprod Validation

### Preprod Deployment Process

```
dev/backend branches → master branch → Automatic deployment to Preprod
```

### Frontend Preprod

**Process:**

1. Create PR from `dev` to `master`
2. **Automated CI/CD triggers:**
   - Workflow: `.github/workflows/pre_web.yml`
   - Deploys to Firebase Hosting (Preprod project)

### Backend Preprod

**Process:**

1. Create PR from `backend` to `master`
2. **Automated CI/CD triggers:**
   - Workflow: `.github/workflows/pre_backend.yml`
   - Deploys to Google Cloud Run (Preprod project)

### **Manual QA Requirements**

- Perform manual QA by a **different team member** than the developer
- Test the new feature thoroughly
- Run regression checks on key flows
- Monitor logs for issues
- Use mock/staging services where applicable
- Optionally test behind a **feature flag**

### **✅ Functional Requirements Confirmation**

- This phase happens **after** preprod deployment
- Product team validates the implementation
- Either approved or needs reshaping (depends on requirements)
- If reshaping needed: deploy again to dev → get approval → continue deploy process

## 🧷 5. Functional/Integration Testing

- For new features or bug fixes:
  - Write/update functional or integration tests
  - Ensure these are part of the CI pipeline for preprod
- **Frontend Testing Tools:**
  - Unit tests: Jest (currently implemented)
  - Functional tests: Playwright (planned implementation)
- **Backend Testing Tools:**
  - Unit tests: Go test with race detection
  - Integration tests: Supertest, Postman
  - API testing: Custom Go tests

## 🤝 6. Production Deployment (Peer-Based)

### Production Deployment Requirements

- **Minimum 2 developers required** for production deployment
- Once preprod testing passes and QA approval received

### Frontend Production

```
master branch → Create production tag → Automatic deployment to Production
```

**Process:**

1. Create production tag: `v[0-9].[0-9]+.[0-9]+-web` (e.g., `v1.2.3-web`)
2. **Automated CI/CD triggers:**
   - Workflow: `.github/workflows/prod_web.yml`
   - Deploys to Firebase Hosting (Production project)

### Backend Production

```
master branch → Create production tag → Automatic deployment to Production
```

**Process:**

1. Create production tag: `v[0-9].[0-9]+.[0-9]+-backend` (e.g., `v1.2.3-backend`)
2. **Automated CI/CD triggers:**
   - Workflow: `.github/workflows/prod_backend.yml`
   - Deploys to Google Cloud Run (Production project)

### **Release Tag Creation**

- Create **release tag** with semantic versioning: `vX.Y.Z`
- Include comprehensive changelog
- Deploy with **at least one other developer present**

## 🌟 Demo Environment

### Frontend Demo

**Trigger:** Tag `v[0-9].[0-9]+.[0-9]+-demoWeb`  
**Workflow:** `.github/workflows/demo_web.yml`  
**Purpose:** Client demonstrations and presentations

### Backend Demo

**Trigger:** Tag `v[0-9].[0-9]+.[0-9]+-demoBackend`  
**Workflow:** `.github/workflows/demo_backend.yml`  
**Purpose:** Backend API demonstrations for clients

## 🚦 7. Post-Deployment Smoke Testing

### Immediate Post-Deploy Actions

- Perform smoke tests on core features:
  - Login functionality
  - Dashboard loading
  - Key user flows
  - API endpoints health checks
- Monitor logs and app metrics for **15–30 minutes**
- Keep all developers available to respond quickly to any issues

### Monitoring Checklist

- ✅ Application starts successfully
- ✅ Database connections working
- ✅ External API integrations functional
- ✅ No critical errors in logs
- ✅ Performance metrics within acceptable ranges

## 🔙 8. Rollback Strategy

### Frontend Rollback

- **Issue Detection:** Critical frontend issues arise
- **Process:** Revert to the **previous stable tag** and redeploy
- **Timeline:** Immediate rollback capability via Firebase Console

### Backend Rollback (Faster Process)

- **Issue Detection:** Critical backend issues arise
- **Process:**
  1. **Immediate:** Roll back or ping the existing Cloud Run of prod env to head-1 version (previous version)
  2. **Fix:** Take needed time to fix the issue
  3. **Redeploy:** Re-integrate through common process (same as frontend deploy)
- **Timeline:** Faster rollback via GCP Cloud Run version management

### Emergency Procedures

1. Identify and assess the issue severity
2. Execute appropriate rollback strategy (FE or BE)
3. Log the issue and notify the entire team
4. Apply a patch fix via the same review and deploy process
5. Post-incident review and documentation update

## 🛠️ Manual Deployment Commands

### Backend Manual Deployment

Located in `backend/makefile`:

```bash
# Build Docker image
make build

# Push to registry
make push

# Deploy to Cloud Run
make deploy

# Deploy equipment uploader service
make deploy-equipment-uploader
```

### Frontend Manual Deployment

Located in `package.json`:

```bash
# Deploy to Firebase Hosting
yarn deploy
# or
npm run deploy
```

## 🔧 Environment Configuration

### Backend Environment Variables

Each environment uses different configurations:

- `MODE=production`
- `GOOGLE_CLOUD_PROJECT` (environment-specific)
- `FIREBASE_API_KEY` (environment-specific)
- `FIREBASE_STORAGE_BUCKET` (environment-specific)
- `EQUIPMENT_LIBRARY_URL` (environment-specific)
- `SENDGRID_API_KEY`
- `SENDGRID_SENDER`
- `SENDGRID_FORGOT_PASSWORD_TEMPLATE_ID`
- `SLACK_WEBHOOK_URL`

### Frontend Environment Variables

Environment-specific variables in `.env`:

- `VITE_REACT_APP_BASE_URL` (environment-specific)
- `VITE_ALGOLIA_ID`
- `VITE_ALGOLIA_API_KEY`
- `VITE_ALGOLIA_INDEX_NAME` (environment-specific)
- `VITE_ALGOLIA_INDEX_NAME_STANDARDS` (environment-specific)
- `VITE_DERENTAL_BIDZ_EMAIL`
- `VITE_DERENTAL_SUPER_ADMIN_PASSWORD`
- `VITE_DERENTAL_BIDZ_STORAGE_EQUIPMENTS_IMG_PATH`

## 🛡️ Quality Gates

### Pre-commit Hooks

- **Husky** pre-commit hook runs `npm run lint`
- Located in `.husky/pre-commit`
- Ensures code quality before commits

### Automated Testing

- **Frontend:** Jest tests, ESLint, build verification
- **Backend:** Go tests with race detection, golangci-lint
- **CI/CD Integration:** All tests must pass before deployment

## 🏗️ Infrastructure Details

### Google Cloud Platform

- **Container Registry:** `us-central1-docker.pkg.dev`
- **Cloud Run:** Serverless container deployment
- **Region:** `us-central1`
- **Scaling:** 0-30 instances, 500 concurrency
- **Execution Environment:** gen2

### Firebase

- **Hosting:** Frontend deployment platform
- **Storage:** File storage for equipment images
- **Authentication:** User management system

## 🛠️ Tooling Recommendations

### CI/CD

- **GitHub Actions** (currently implemented)
- Automated workflows for build, test, and deploy

### Testing

- **Frontend:**
  - Unit tests: Jest (currently implemented)
  - Functional tests: Playwright (planned implementation)
- **Backend:**
  - Unit tests: Go test with race detection
  - Integration tests: Supertest, Postman
  - API testing: Custom Go tests

### Monitoring

- **GCP Monitoring:** Cloud Run metrics and logs
- **Firebase Analytics:** Frontend performance monitoring
- **Slack Integration:** Deployment notifications via webhooks

### Communication

- **Slack:** Use dedicated channels for deployment coordination
- **Channel Strategy:** Create specific channels for releases and alerts
- **Centralized Information:** Keep deployment info in dedicated channels

## ✅ Release Checklist

| Step                             | Responsible  | Frontend | Backend | Done |
| -------------------------------- | ------------ | -------- | ------- | ---- |
| PR created & reviewed            | Dev 1 & 2    | ☐        | ☐       | ☐    |
| CI checks passed                 | Automated    | ☐        | ☐       | ☐    |
| Deploy to Dev                    | Automated    | ☐        | ☐       | ☐    |
| Product validation               | Product Team | ☐        | ☐       | ☐    |
| Merge to `master`                | Lead Dev     | ☐        | ☐       | ☐    |
| Deploy to Preprod                | Automated    | ☐        | ☐       | ☐    |
| Manual QA testing                | Dev 3        | ☐        | ☐       | ☐    |
| Functional tests updated         | Dev 1        | ☐        | ☐       | ☐    |
| Tag created + peer deploy        | Dev 1 & 2    | ☐        | ☐       | ☐    |
| Post-deploy smoke test           | Dev 3        | ☐        | ☐       | ☐    |
| Monitor app + rollback if needed | All Devs     | ☐        | ☐       | ☐    |

## 🚨 Troubleshooting

### Common Issues

#### Build Failures

- **Frontend:** Check Node.js version, yarn dependencies, environment variables
- **Backend:** Check Go version, module dependencies, Docker configuration
- **Solution:** Verify CI/CD environment matches local development

#### Deployment Timeouts

- **GCP Issues:** Verify quotas, permissions, and resource limits
- **Firebase Issues:** Check hosting configuration and build size
- **Solution:** Monitor GCP Console and Firebase Console for errors

#### Environment Mismatches

- **Configuration:** Validate environment-specific variables
- **Database:** Check connection strings and credentials
- **Solution:** Compare working environment configs

#### Database Connection Issues

- **Firestore:** Check firewall rules and service account permissions
- **Cloud SQL:** Verify connection pools and network settings
- **Solution:** Review GCP IAM and network configurations

### Emergency Contacts & Procedures

- **DevOps Issues:** Check GCP Console and Cloud Run logs
- **Frontend Issues:** Check Firebase Console and hosting logs
- **Backend Issues:** Check Cloud Run logs and error reporting
- **Database Issues:** Check Firestore console and connection metrics

## 📋 Best Practices

### Development

1. **Always test in Dev environment first**
2. **Use feature branches for all development**
3. **Write descriptive commit messages**
4. **Keep commits atomic and focused**
5. **Update tests with code changes**

### Deployment

1. **Use staging for final validation**
2. **Tag releases with semantic versioning**
3. **Monitor deployments for issues**
4. **Keep environment configurations in sync**
5. **Document all changes in changelogs**

### Team Coordination

1. **Minimum 2 developers for production deployments**
2. **Communicate deployment plans in advance**
3. **Keep team available during deployments**
4. **Document issues and resolutions**
5. **Conduct post-incident reviews**

---

_Keep this document version-controlled and update it as the team or product evolves._

**Last Updated:** 2025-01-19
**Document Version:** 1.0
**Created By:** MehdiMans
**Maintained By:** Development Team
